#!/usr/bin/env python3
"""
Test script to verify the locator resolution fix works correctly.

This script tests the specific case identified in the debug logs where
name attributes were being bypassed due to the data format detection bug.
"""

import sys
import os
import json
from typing import Dict, Any, List

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the fixed locator resolver
from core.locator_resolver import resolve_locator_conflicts

def test_tc001_failing_case():
    """
    Test the exact TC_001 case that was failing before the fix.
    
    This case has:
    - Element with name="username" attribute
    - Raw match with locator_strategy="css" and locator="input.oxd-input"
    - Should now prioritize name strategy over CSS strategy
    """
    print("🧪 Testing TC_001 failing case (name attribute should be prioritized)...")
    
    # Recreate the exact input data from the failing logs
    step_data = {
        "locator_strategy": "id",
        "locator": "username",
        "action": "type",
        "step_no": "2"
    }
    
    element_matches = [
        {
            "index": 0,
            "score": 1.0,
            "manually_selected": True,
            "element_data": {
                "selector": "input.oxd-input",
                "xpath": "/body/div/div/div/div/div/div/div/form/div/div/div/input",
                "attributes": {
                    "id": "",
                    "name": "username",  # ← This should be prioritized!
                    "class": "oxd-input oxd-input--focus gretah-element-highlight",
                    "type": "",
                    "value": "",
                    "placeholder": "Username",
                    "href": "",
                    "role": "",
                    "aria-label": "",
                    "text": ""
                }
            },
            "raw_match": {
                "element": {
                    "name": "input",
                    "tag": "input",
                    "selector": "input.oxd-input",
                    "xpath": "/body/div/div/div/div/div/div/div/form/div/div/div/input",
                    "attributes": {
                        "id": "",
                        "name": "username",  # ← This should be prioritized!
                        "class": "oxd-input oxd-input--focus gretah-element-highlight",
                        "type": "",
                        "value": "",
                        "placeholder": "Username",
                        "href": "",
                        "role": "",
                        "aria-label": "",
                        "text": ""
                    },
                    "interactive": True,
                    "visible": True,
                    "manually_selected": True,
                    "score": 100
                },
                "score": 1.0,
                "manually_selected": True,
                "locator_strategy": "css",  # ← This was causing the bypass
                "locator": "input.oxd-input"
            }
        }
    ]
    
    # Debug: Print input data structure
    print(f"🔍 Debug - Input element_matches structure:")
    print(f"   Count: {len(element_matches)}")
    for i, match in enumerate(element_matches):
        print(f"   Match {i}: keys = {list(match.keys())}")
        if 'element_data' in match:
            print(f"     element_data.attributes.name = {match['element_data']['attributes'].get('name')}")
        if 'raw_match' in match:
            print(f"     raw_match has locator_strategy = {'locator_strategy' in match['raw_match']}")
            print(f"     raw_match has locator = {'locator' in match['raw_match']}")

    # Run the resolver
    result = resolve_locator_conflicts(
        step_data=step_data,
        element_matches=element_matches,
        step_no="2",
        test_case_id="TC_001_FIX_TEST"
    )
    
    # Analyze results
    print(f"📊 Resolution Results:")
    print(f"   Strategy: {result.get('resolved_locator_strategy')}")
    print(f"   Locator: {result.get('resolved_locator')}")
    print(f"   Confidence: {result.get('confidence_score', 0):.3f}")
    print(f"   Reason: {result.get('resolution_reason')}")
    
    # Check if fix worked
    expected_strategy = "name"
    expected_locator = "username"
    expected_min_confidence = 0.40  # Should be 40%+ for name strategy
    
    success = (
        result.get('resolved_locator_strategy') == expected_strategy and
        result.get('resolved_locator') == expected_locator and
        result.get('confidence_score', 0) >= expected_min_confidence
    )
    
    if success:
        print("✅ SUCCESS: Name strategy correctly prioritized!")
        print(f"   Expected: {expected_strategy} strategy with {expected_min_confidence:.0%}+ confidence")
        print(f"   Got: {result.get('resolved_locator_strategy')} strategy with {result.get('confidence_score', 0):.1%} confidence")
    else:
        print("❌ FAILURE: Name strategy not prioritized correctly")
        print(f"   Expected: {expected_strategy} strategy with {expected_min_confidence:.0%}+ confidence")
        print(f"   Got: {result.get('resolved_locator_strategy')} strategy with {result.get('confidence_score', 0):.1%} confidence")
    
    return success

def test_css_selector_name_extraction():
    """
    Test CSS selector with name attribute extraction.
    
    This should extract the name from input[name="username"] and prioritize it.
    """
    print("\n🧪 Testing CSS selector name extraction...")
    
    step_data = {
        "locator_strategy": "css",
        "locator": "input.form-control"
    }
    
    element_matches = [
        {
            "element": {
                "selector": "input[name=\"username\"]",  # ← Should extract "username"
                "attributes": {
                    "name": "username",
                    "class": "form-control"
                }
            },
            "score": 0.75,
            "manually_selected": False
        }
    ]
    
    result = resolve_locator_conflicts(
        step_data=step_data,
        element_matches=element_matches,
        step_no="1",
        test_case_id="CSS_NAME_EXTRACT_TEST"
    )
    
    print(f"📊 Resolution Results:")
    print(f"   Strategy: {result.get('resolved_locator_strategy')}")
    print(f"   Locator: {result.get('resolved_locator')}")
    print(f"   Confidence: {result.get('confidence_score', 0):.3f}")
    
    success = (
        result.get('resolved_locator_strategy') == "name" and
        result.get('resolved_locator') == "username"
    )
    
    if success:
        print("✅ SUCCESS: Name extracted from CSS selector!")
    else:
        print("❌ FAILURE: Name not extracted from CSS selector")
    
    return success

def main():
    """Run all tests and report results."""
    print("🔧 Testing Locator Resolution Fix")
    print("=" * 50)
    
    tests = [
        ("TC_001 Failing Case", test_tc001_failing_case),
        ("CSS Selector Name Extraction", test_css_selector_name_extraction)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success, None))
        except Exception as e:
            print(f"❌ ERROR in {test_name}: {e}")
            results.append((test_name, False, str(e)))
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Summary:")
    
    passed = 0
    for test_name, success, error in results:
        if success:
            print(f"✅ {test_name}: PASSED")
            passed += 1
        else:
            print(f"❌ {test_name}: FAILED" + (f" ({error})" if error else ""))
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! The locator resolution fix is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. The fix may need additional work.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
